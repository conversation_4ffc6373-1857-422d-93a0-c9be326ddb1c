<!-- 物料入库管理 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleOnlineInbound"
          v-has-permi="['materielManage:inbound:add']"
          >在线入库</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchInbound"
          v-has-permi="['materielManage:inbound:batchImport']"
          >批量入库</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['materielManage:inbound:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询</el-button
          >
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置</el-button
          >
        </div>
      </template>
    </BuseCrud>

    <!-- 批量上传组件 -->
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量入库"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.5"
      maxSizeText="500m"
    >
    </BatchUpload>

    <!-- 在线入库抽屉 -->
    <InboundDrawer ref="inboundDrawer" @success="handleQuery" />
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/materielManage/inbound.js";
import exportMixin from "@/mixin/export.js";
import BatchUpload from "@/components/BatchUpload/index.vue";
import InboundDrawer from "./components/InboundDrawer.vue";
import { listUser } from "@/api/common.js";
import warehouseApi from "@/api/materielManage/warehouse.js";

import { operStatusDict } from "@/views/materielManage/config.js";
export default {
  name: "InboundPage",
  components: { BatchUpload, InboundDrawer },
  mixins: [exportMixin],
  data() {
    return {
      uploadObj: {
        api: "/wlInBound/import",
        url: "/charging-maintenance-ui/static/物料入库批量导入模板.xlsx",
        extraData: {},
      },
      // 字典选项
      statusOptions: [],
      warehouseOptions: [],
      warehouseCategoryOptions: [],
      inboundCategoryOptions: [],
      operatorOptions: [],
      usableStatusOptions: [],

      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "boundId",
          isCurrent: true,
        },
        spanMethod: this.mergeRowMethod,
      },
      tableData: [],
      tableColumn: [
        {
          field: "operNo",
          title: "入库单号",
          width: 150,
        },
        {
          field: "materialName",
          title: "物料名称",
          width: 150,
        },
        {
          field: "materialNo",
          title: "物料编码",
          width: 120,
        },
        {
          field: "materialModel",
          title: "规格型号",
          width: 120,
        },
        {
          field: "unit",
          title: "单位",
          width: 80,
          formatter: ({ cellValue }) => {
            return (
              this.unitOptions?.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "boundNum",
          title: "入库数量",
          width: 100,
        },
        {
          field: "unitPrice",
          title: "单价（元）",
          width: 100,
          formatter: ({ cellValue }) => {
            return cellValue ? `¥${cellValue}` : "-";
          },
        },
        {
          field: "materialFactory",
          title: "生产厂家",
          width: 150,
        },
        {
          field: "operStatus",
          title: "状态",
          width: 100,
          formatter: ({ cellValue }) => {
            return (
              operStatusDict.find((x) => x.value == cellValue)?.label ||
              cellValue
            );
          },
        },
        {
          field: "inRepositoryName",
          title: "入库仓库",
          width: 120,
        },
        {
          field: "repositoryCategory",
          title: "仓库类别",
          width: 120,
        },
        {
          field: "inCategory",
          title: "入库类别",
          width: 120,
          formatter: ({ cellValue }) => {
            return (
              this.inboundCategoryOptions?.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "operName",
          title: "入库人",
          width: 100,
        },
        {
          field: "operTimeStr",
          title: "入库时间",
          width: 150,
        },
        {
          field: "purchaseNum",
          title: "采购单号/工单单号",
          width: 150,
        },
        {
          field: "usableStatus",
          title: "可用状态",
          width: 100,
          formatter: ({ cellValue }) => {
            return (
              this.usableStatusOptions?.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      unitOptions: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        config: [
          {
            field: "operNo",
            element: "el-input",
            title: "入库单号",
            props: {
              placeholder: "请输入入库单号",
            },
          },
          {
            field: "materialName",
            element: "el-input",
            title: "物料名称",
            props: {
              placeholder: "请输入物料名称",
            },
          },
          {
            field: "materialFactory",
            element: "el-input",
            title: "生产厂家",
            props: {
              placeholder: "请输入生产厂家",
            },
          },
          {
            field: "operStatus",
            element: "el-select",
            title: "状态",
            props: {
              options: this.statusOptions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择状态",
            },
          },
          {
            field: "operTimeRange",
            element: "el-date-picker",
            title: "入库时间",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "inRepositoryName",
            element: "el-select",
            title: "入库仓库",
            props: {
              options: this.warehouseOptions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择入库仓库",
            },
          },
          {
            field: "materialNo",
            element: "el-input",
            title: "物料编码",
            props: {
              placeholder: "请输入物料编码",
            },
          },
          {
            field: "repositoryCategory",
            element: "el-select",
            title: "仓库类别",
            props: {
              options: this.warehouseCategoryOptions,
              placeholder: "请选择仓库类别",
            },
          },
          {
            field: "operName",
            element: "el-select",
            title: "入库人",
            props: {
              options: this.operatorOptions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择入库人",
            },
          },
          {
            field: "inCategory",
            element: "el-select",
            title: "入库类别",
            props: {
              options: this.inboundCategoryOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              placeholder: "请选择入库类别",
            },
          },
          {
            field: "materialModel",
            element: "el-input",
            title: "规格型号",
            props: {
              placeholder: "请输入规格型号",
            },
          },
          {
            field: "purchaseNum",
            element: "el-input",
            title: "采购单号",
            props: {
              placeholder: "请输入采购单号",
            },
          },
          {
            field: "usableStatus",
            element: "el-select",
            title: "可用状态",
            props: {
              options: this.usableStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              placeholder: "请选择可用状态",
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        modalWidth: "50%",
        addTitle: "新增入库",
        editTitle: "编辑入库",
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["materielManage:inbound:edit"]),
        delBtn: checkPermission(["materielManage:inbound:delete"]),
        menu: true,
        menuWidth: 200,
        menuFixed: "right",
        formConfig: [],
        customOperationTypes: [
          // {
          //   title: "编辑",
          //   typeName: "edit",
          //   event: (row) => {
          //     return this.rowEdit(row);
          //   },
          //   condition: () => {
          //     return checkPermission(["materielManage:inbound:edit"]);
          //   },
          // },
          // {
          //   title: "删除",
          //   typeName: "delete",
          //   event: (row) => {
          //     return this.deleteRowHandler(row);
          //   },
          //   condition: () => {
          //     return checkPermission(["materielManage:inbound:delete"]);
          //   },
          // },
        ],
      };
    },
  },
  created() {
    this.initDictData();
    this.loadData();
  },
  methods: {
    checkPermission,

    // 初始化字典数据
    async initDictData() {
      try {
        // 获取状态字典
        this.statusOptions = operStatusDict;
        this.getDicts("wl_material_unit").then((response) => {
          this.unitOptions = response.data;
        });

        // 获取字典数据
        const [inboundCategoryRes, usableStatusRes] = await Promise.all([
          this.getDicts("wl_inbound_type"),
          this.getDicts("wl_usable_status"),
        ]);

        this.inboundCategoryOptions = inboundCategoryRes.data;
        this.usableStatusOptions = usableStatusRes.data;

        // 获取仓库列表
        this.loadWarehouseOptions();
        // 获取操作人员列表
        this.getListUser();
        warehouseApi
          .queryWarehouseType({ type: "repositoryCategory" })
          .then((response) => {
            this.warehouseCategoryOptions = response.data?.map((x) => {
              return { label: x, value: x };
            });
          });
      } catch (error) {
        console.error("初始化字典数据失败:", error);
      }
    },

    // 加载仓库选项
    async loadWarehouseOptions() {
      try {
        const res = await api.queryWarehouseList();
        this.warehouseOptions =
          res.data?.map((item) => ({
            label: item.repositoryName,
            value: item.repositoryName,
          })) || [];
      } catch (error) {
        console.error("加载仓库选项失败:", error);
      }
    },

    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.operatorOptions = data;
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "operTimeRange",
          title: "操作时间",
          startFieldName: "operTimeStart",
          endFieldName: "operTimeEnd",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    // 加载数据
    async loadData() {
      this.loading = true;
      try {
        // 处理时间范围参数
        let params = { ...this.params };
        this.handleTimeRange(params);

        const res = await api.getTableData({
          ...params,
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
        });
        this.tableData = res.data || [];
        this.tablePage.total = res.total || 0;
      } catch (error) {
        this.$message.error(`加载数据失败：${error.message || "网络错误"}`);
      } finally {
        this.loading = false;
      }
    },

    // 行合并方法（相同入库单号的行合并指定列）
    mergeRowMethod({ row, column, rowIndex }) {
      const mergeFields = [
        "operNo",
        "operStatus",
        "inRepositoryName",
        "repositoryCategory",
        "inCategory",
        "operName",
        "operTimeStr",
        "purchaseNum",
      ];

      if (mergeFields.includes(column.field)) {
        const currentOperNo = row.operNo;
        let rowspan = 1;
        let colspan = 1;

        // 向上查找相同入库单号的行
        for (let i = rowIndex - 1; i >= 0; i--) {
          if (this.tableData[i].operNo === currentOperNo) {
            return { rowspan: 0, colspan: 0 };
          } else {
            break;
          }
        }

        // 向下查找相同入库单号的行
        for (let i = rowIndex + 1; i < this.tableData.length; i++) {
          if (this.tableData[i].operNo === currentOperNo) {
            rowspan++;
          } else {
            break;
          }
        }

        return { rowspan, colspan };
      }
    },

    // 在线入库
    handleOnlineInbound() {
      this.$refs.inboundDrawer.open();
    },

    // 批量入库
    handleBatchInbound() {
      this.$refs.batchUpload.open();
    },

    // 编辑行
    rowEdit(row) {
      this.$refs.inboundDrawer.open(row);
    },

    // 删除行
    async deleteRowHandler(row) {
      try {
        await this.$confirm(
          `确认删除入库单号"${row.operNo}"的物料"${row.materialName}"吗？\n\n删除后无法恢复！`,
          "警告",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            dangerouslyUseHTMLString: true,
          }
        );

        await api.remove(row);
        this.loadData();
        this.$message.success("删除成功");
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error(`删除失败：${error.message || "网络错误"}`);
        }
      }
    },

    // 模态框确认处理
    async modalConfirmHandler() {
      // 这里暂时不处理，因为使用抽屉组件
    },

    // 查询
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    // 重置
    handleReset() {
      this.params = {};
      this.handleQuery();
    },

    // 导出
    handleExport() {
      const params = {
        ...this.params,
      };
      this.handleTimeRange(params);

      this.handleCommonExport(api.export, params);
    },
  },
};
</script>

<style lang="less" scoped>
.card-container {
  padding: 20px;
  background: #fff;
  height: 100%;
}
.btn-wrap {
  text-align: right;
  padding: 0 20px 20px;
}
</style>
