<template>
  <el-drawer title="库存详情" :visible.sync="drawerVisible" size="80%">
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>基本信息</span>
      </div>
      <BaseDescriptions :list="baseList" :column="2"></BaseDescriptions>
    </el-card>

    <!-- 流动记录卡片 -->
    <el-card style="margin-top: 20px">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>流动记录</span>
      </div>

      <el-tabs v-model="activeTab" type="card">
        <!-- 库存明细 -->
        <el-tab-pane label="库存明细" name="inventory">
          <BuseCrud
            ref="inventoryCrud"
            :loading="inventoryLoading"
            :filterOptions="inventoryFilterOptions"
            :tablePage="inventoryTablePage"
            :tableColumn="inventoryTableColumn"
            :tableData="inventoryTableData"
            :tableProps="inventoryTableProps"
            :modalConfig="inventoryModalConfig"
            :tabRadioList="inventoryTabRadioList"
            @tabRadioChange="inventoryTabRadioChange"
            @loadData="loadInventoryData"
          >
            <template slot="filterCustomBtn">
              <div class="btn-wrap">
                <el-button
                  type="primary"
                  icon="el-icon-download"
                  @click="handleInventoryExport"
                >
                  导出
                </el-button>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="handleInventoryQuery"
                >
                  查询
                </el-button>
                <el-button icon="el-icon-refresh" @click="handleInventoryReset">
                  重置
                </el-button>
              </div>
            </template>
          </BuseCrud>
        </el-tab-pane>

        <!-- 入库记录 -->
        <el-tab-pane label="入库记录" name="inbound">
          <BuseCrud
            ref="inboundCrud"
            :loading="inboundLoading"
            :filterOptions="inboundFilterOptions"
            :tablePage="inboundTablePage"
            :tableColumn="inboundTableColumn"
            :tableData="inboundTableData"
            :tableProps="inboundTableProps"
            :modalConfig="inboundModalConfig"
            @loadData="loadInboundData"
          >
            <template slot="filterCustomBtn">
              <div class="btn-wrap">
                <el-button
                  type="primary"
                  icon="el-icon-download"
                  @click="handleInboundExport"
                >
                  导出
                </el-button>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="handleInboundQuery"
                >
                  查询
                </el-button>
                <el-button icon="el-icon-refresh" @click="handleInboundReset">
                  重置
                </el-button>
              </div>
            </template>
          </BuseCrud>
        </el-tab-pane>

        <!-- 出库记录 -->
        <el-tab-pane label="出库记录" name="outbound">
          <BuseCrud
            ref="outboundCrud"
            :loading="outboundLoading"
            :filterOptions="outboundFilterOptions"
            :tablePage="outboundTablePage"
            :tableColumn="outboundTableColumn"
            :tableData="outboundTableData"
            :tableProps="outboundTableProps"
            :modalConfig="outboundModalConfig"
            @loadData="loadOutboundData"
          >
            <template slot="filterCustomBtn">
              <div class="btn-wrap">
                <el-button
                  type="primary"
                  icon="el-icon-download"
                  @click="handleOutboundExport"
                >
                  导出
                </el-button>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="handleOutboundQuery"
                >
                  查询
                </el-button>
                <el-button icon="el-icon-refresh" @click="handleOutboundReset">
                  重置
                </el-button>
              </div>
            </template>
          </BuseCrud>
        </el-tab-pane>

        <!-- 调拨记录 -->
        <el-tab-pane label="调拨记录" name="allocate">
          <BuseCrud
            ref="allocateCrud"
            :loading="allocateLoading"
            :filterOptions="allocateFilterOptions"
            :tablePage="allocateTablePage"
            :tableColumn="allocateTableColumn"
            :tableData="allocateTableData"
            :tableProps="allocateTableProps"
            :modalConfig="allocateModalConfig"
            @loadData="loadAllocateData"
          >
            <template slot="filterCustomBtn">
              <div class="btn-wrap">
                <el-button
                  type="primary"
                  icon="el-icon-download"
                  @click="handleAllocateExport"
                >
                  导出
                </el-button>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="handleAllocateQuery"
                >
                  查询
                </el-button>
                <el-button icon="el-icon-refresh" @click="handleAllocateReset">
                  重置
                </el-button>
              </div>
            </template>
          </BuseCrud>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </el-drawer>
</template>

<script>
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import api from "@/api/materielManage/warehouse.js";
import inboundApi from "@/api/materielManage/inbound.js";
import outboundApi from "@/api/materielManage/outbound.js";
import allocateApi from "@/api/materielManage/allocate.js";
import { initParams } from "@/utils/buse.js";
import { listUser } from "@/api/common.js";
import { operStatusDict } from "@/views/materielManage/config.js";

export default {
  components: { BaseDescriptions },
  data() {
    return {
      drawerVisible: false,
      baseData: {},
      activeTab: "inventory",

      // 库存明细相关数据
      inventoryLoading: false,
      inventoryTableData: [],
      inventoryTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      inventoryParams: {},
      inventoryTabRadioList: [],
      inventoryCurrentTab: "1",

      // 入库记录相关数据
      inboundLoading: false,
      inboundTableData: [],
      inboundTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      inboundParams: {},

      // 出库记录相关数据
      outboundLoading: false,
      outboundTableData: [],
      outboundTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      outboundParams: {},

      // 调拨记录相关数据
      allocateLoading: false,
      allocateTableData: [],
      allocateTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      allocateParams: {},

      // 字典选项数据
      statusOptions: operStatusDict,
      warehouseOptions: [],
      warehouseCategoryOptions: [],
      inboundCategoryOptions: [],
      outboundCategoryOptions: [],
      operatorOptions: [],
      usableStatusOptions: [],
      unitOptions: [],
    };
  },
  computed: {
    baseList() {
      return [
        {
          title: "仓库名称",
          value: this.baseData.repositoryName || "-",
        },
        {
          title: "仓库类别",
          value: this.baseData.repositoryCategory || "-",
        },
        {
          title: "当前库存数量",
          value: this.baseData.inventoryQuantity || "0",
        },
        {
          title: "仓库状态",
          value: this.baseData.status === "0" ? "启用" : "停用",
        },
      ];
    },

    // 库存明细配置
    inventoryFilterOptions() {
      return {
        showCount: 4,
        layout: "right",
        inline: true,
        labelWidth: "100px",
        config: [
          {
            field: "materialName",
            element: "el-input",
            title: "物料名称",
            props: { placeholder: "请输入物料名称" },
          },
          {
            field: "materialNo",
            element: "el-input",
            title: "物料编码",
            props: { placeholder: "请输入物料编码" },
          },
          {
            field: "materialFactory",
            element: "el-input",
            title: "生产厂家",
            props: { placeholder: "请输入生产厂家" },
          },
          {
            field: "materialModel",
            element: "el-input",
            title: "规格型号",
            props: { placeholder: "请输入规格型号" },
          },
        ],
        params: this.inventoryParams,
      };
    },

    inventoryTableColumn() {
      return [
        { field: "materialName", title: "物料名称" },
        { field: "materialNo", title: "物料编码" },
        { field: "materialModel", title: "规格型号" },
        { field: "unit", title: "单位" },
        { field: "quantityNum", title: "数量" },
        {
          field: "unitPrice",
          title: "单价（元）",
        },
        { field: "materialFactory", title: "生产厂家" },
      ];
    },

    inventoryTableProps() {
      return {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
      };
    },

    inventoryModalConfig() {
      return { menu: false, addBtn: false };
    },

    // 入库记录配置
    inboundFilterOptions() {
      return {
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        config: [
          {
            field: "operNo",
            element: "el-input",
            title: "入库单号",
            props: {
              placeholder: "请输入入库单号",
            },
          },
          {
            field: "materialName",
            element: "el-input",
            title: "物料名称",
            props: {
              placeholder: "请输入物料名称",
            },
          },
          {
            field: "materialFactory",
            element: "el-input",
            title: "生产厂家",
            props: {
              placeholder: "请输入生产厂家",
            },
          },
          {
            field: "inRepositoryName",
            element: "el-select",
            title: "入库仓库",
            props: {
              options: this.warehouseOptions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择入库仓库",
            },
          },
          {
            field: "materialNo",
            element: "el-input",
            title: "物料编码",
            props: {
              placeholder: "请输入物料编码",
            },
          },
          {
            field: "repositoryCategory",
            element: "el-select",
            title: "仓库类别",
            props: {
              options: this.warehouseCategoryOptions,
              placeholder: "请选择仓库类别",
            },
          },
          {
            field: "inCategory",
            element: "el-select",
            title: "入库类别",
            props: {
              options: this.inboundCategoryOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              placeholder: "请选择入库类别",
            },
          },
          {
            field: "materialModel",
            element: "el-input",
            title: "规格型号",
            props: {
              placeholder: "请输入规格型号",
            },
          },
          {
            field: "purchaseNum",
            element: "el-input",
            title: "采购单号",
            props: {
              placeholder: "请输入采购单号",
            },
          },
          {
            field: "operStatus",
            element: "el-select",
            title: "状态",
            props: {
              options: this.statusOptions,
              placeholder: "请选择状态",
            },
          },

          {
            field: "operName",
            element: "el-select",
            title: "入库人",
            props: {
              options: this.operatorOptions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择入库人",
            },
          },

          {
            field: "usableStatus",
            element: "el-select",
            title: "可用状态",
            props: {
              options: this.usableStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              placeholder: "请选择可用状态",
            },
          },
          {
            field: "operTimeRange",
            element: "el-date-picker",
            title: "入库时间",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
        ],
        params: this.inboundParams,
      };
    },

    inboundTableColumn() {
      return [
        { field: "operNo", title: "入库单号", width: 150 },
        {
          field: "inCategory",
          title: "入库类别",
          width: 120,
          formatter: ({ cellValue }) => {
            return (
              this.inboundCategoryOptions?.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        { field: "operName", title: "入库人", width: 100 },
        { field: "operTimeStr", title: "入库时间", width: 150 },
        { field: "purchaseNum", title: "采购单号/工单单号", width: 150 },
        { field: "materialName", title: "物料名称", width: 150 },
        { field: "materialModel", title: "规格型号", width: 120 },
        {
          field: "unit",
          title: "单位",
          width: 80,
          formatter: ({ cellValue }) => {
            return (
              this.unitOptions?.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        { field: "boundNum", title: "数量", width: 100 },
        {
          field: "unitPrice",
          title: "单价（元）",
          width: 100,
        },
        { field: "materialFactory", title: "生产厂家", width: 150 },
        { field: "materialNo", title: "物料编码", width: 120 },
        {
          field: "usableStatus",
          title: "可用状态",
          width: 100,
          formatter: ({ cellValue }) => {
            return (
              this.usableStatusOptions?.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
      ];
    },

    inboundTableProps() {
      return {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        spanMethod: this.mergeRowMethod,
      };
    },

    inboundModalConfig() {
      return { menu: false, addBtn: false };
    },

    // 出库记录配置
    outboundFilterOptions() {
      return {
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        config: [
          {
            field: "operNo",
            element: "el-input",
            title: "出库单号",
            props: { placeholder: "请输入出库单号" },
          },
          {
            field: "materialName",
            element: "el-input",
            title: "物料名称",
            props: { placeholder: "请输入物料名称" },
          },
          {
            field: "materialFactory",
            element: "el-input",
            title: "生产厂家",
            props: { placeholder: "请输入生产厂家" },
          },
          {
            field: "operStatus",
            element: "el-select",
            title: "状态",
            props: {
              options: this.statusOptions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择状态",
            },
          },
          {
            field: "operTimeRange",
            element: "el-date-picker",
            title: "出库时间",
            props: {
              type: "datetimerange",
              rangeSeparator: "至",
              startPlaceholder: "开始时间",
              endPlaceholder: "结束时间",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
            },
          },
        ],
        params: this.outboundParams,
      };
    },

    outboundTableColumn() {
      return [
        { field: "operNo", title: "出库单号", width: 150 },
        { field: "materialName", title: "物料名称", width: 150 },
        { field: "materialNo", title: "物料编码", width: 120 },
        { field: "materialModel", title: "规格型号", width: 120 },
        { field: "unit", title: "单位", width: 80 },
        { field: "boundNum", title: "出库数量", width: 100 },
        {
          field: "unitPrice",
          title: "单价（元）",
          width: 100,
          formatter: ({ cellValue }) => (cellValue ? `¥${cellValue}` : "-"),
        },
        { field: "materialFactory", title: "生产厂家", width: 150 },
        {
          field: "operStatus",
          title: "状态",
          width: 100,
          formatter: ({ cellValue }) => {
            const statusMap = {
              "1": "暂存",
              "3": "已出库待收货",
              "4": "已出库已收货",
              "5": "待审核",
              "6": "审核通过",
              "7": "审核不通过",
              "9": "已确认收货",
            };
            return statusMap[cellValue] || cellValue;
          },
        },
        { field: "outRepositoryName", title: "出库仓库", width: 120 },
        { field: "outCategory", title: "出库类别", width: 120 },
        { field: "outFlow", title: "出库流向", width: 120 },
        { field: "flowRepositoryName", title: "流向的仓库", width: 120 },
        { field: "operName", title: "出库人", width: 100 },
        { field: "operTimeStr", title: "出库时间", width: 150 },
        { field: "receiver", title: "收件人", width: 100 },
        { field: "receiveAddress", title: "收件地址", width: 200 },
        { field: "trackingNum", title: "快递单号/工单单号", width: 150 },
        { field: "confirmer", title: "确认收货人", width: 100 },
        { field: "confirmTimeStr", title: "确认收货时间", width: 150 },
      ];
    },

    outboundTableProps() {
      return {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        spanMethod: this.mergeRowMethod,
      };
    },

    outboundModalConfig() {
      return { menu: false, addBtn: false };
    },

    // 调拨记录配置
    allocateFilterOptions() {
      return {
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        config: [
          {
            field: "operNo",
            element: "el-input",
            title: "调拨单号",
            props: { placeholder: "请输入调拨单号" },
          },
          {
            field: "materialName",
            element: "el-input",
            title: "物料名称",
            props: { placeholder: "请输入物料名称" },
          },
          {
            field: "materialFactory",
            element: "el-input",
            title: "生产厂家",
            props: { placeholder: "请输入生产厂家" },
          },
          {
            field: "operStatus",
            element: "el-select",
            title: "状态",
            props: {
              options: this.statusOptions,
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择状态",
            },
          },
          {
            field: "operTimeRange",
            element: "el-date-picker",
            title: "调拨申请时间",
            props: {
              type: "datetimerange",
              rangeSeparator: "至",
              startPlaceholder: "开始时间",
              endPlaceholder: "结束时间",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
            },
          },
        ],
        params: this.allocateParams,
      };
    },

    allocateTableColumn() {
      return [
        { field: "operNo", title: "调拨单号", width: 150 },
        { field: "materialName", title: "物料名称", width: 150 },
        { field: "materialNo", title: "物料编码", width: 120 },
        { field: "materialModel", title: "规格型号", width: 120 },
        { field: "unit", title: "单位", width: 80 },
        { field: "boundNum", title: "调拨数量", width: 100 },
        {
          field: "unitPrice",
          title: "单价（元）",
          width: 100,
          formatter: ({ cellValue }) => (cellValue ? `¥${cellValue}` : "-"),
        },
        { field: "materialFactory", title: "生产厂家", width: 150 },
        {
          field: "operStatus",
          title: "状态",
          width: 100,
          formatter: ({ cellValue }) => {
            const statusMap = {
              "1": "暂存",
              "2": "已入库",
              "3": "已出库待收货",
              "4": "已出库已收货",
              "5": "待审核",
              "6": "审核通过",
              "7": "审核不通过",
              "8": "已无效",
              "9": "已确认收货",
            };
            return statusMap[cellValue] || "未知";
          },
        },
        { field: "repositoryName", title: "出库仓库", width: 120 },
        { field: "outCategory", title: "出库类别", width: 120 },
        { field: "remark", title: "调拨原因", width: 150 },
        { field: "flowRepositoryName", title: "入库仓库", width: 120 },
        { field: "inCategory", title: "入库仓库类别", width: 120 },
        { field: "operUser", title: "调拨申请人", width: 120 },
        { field: "operTime", title: "调拨申请时间", width: 150 },
        { field: "receiver", title: "收件人", width: 100 },
        { field: "receiveAddress", title: "收件地址", width: 200 },
        { field: "trackingNum", title: "快递单号", width: 150 },
        { field: "auditUser", title: "审核人", width: 100 },
        { field: "auditTime", title: "审核时间", width: 150 },
      ];
    },

    allocateTableProps() {
      return {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        spanMethod: this.mergeRowMethod,
      };
    },

    allocateModalConfig() {
      return { menu: false, addBtn: false };
    },
  },
  created() {
    this.getDicts("wl_usable_status").then((response) => {
      this.inventoryTabRadioList = response.data?.map((x) => {
        return { label: x.dictLabel, value: x.dictValue };
      });
    });
    this.getDicts("wl_usable_status").then((response) => {
      this.usableStatusOptions = response.data;
    });
    this.getDicts("wl_material_unit").then((response) => {
      this.unitOptions = response.data;
    });

    api.queryWarehouseType({ type: "repositoryCategory" }).then((response) => {
      this.warehouseCategoryOptions = response.data?.map((x) => {
        return { label: x, value: x };
      });
    });
    this.getDicts("wl_inbound_type").then((response) => {
      this.inboundCategoryOptions = response.data;
    });
    this.loadWarehouseOptions();
    this.getListUser();
    this.inventoryParams = initParams(this.inventoryFilterOptions.config);
    this.outboundParams = initParams(this.outboundFilterOptions.config);
    this.allocateParams = initParams(this.allocateFilterOptions.config);
    this.inboundParams = initParams(this.inboundFilterOptions.config);
  },
  methods: {
    open(row) {
      this.baseData = { ...row };
      this.drawerVisible = true;
      this.activeTab = "inventory";
      this.getDetail();
      this.loadInventoryData();
      this.loadInboundData();
      this.loadOutboundData();
      this.loadAllocateData();
    },

    async getDetail() {
      try {
        const res = await api.getDetail({
          repositoryId: this.baseData.repositoryId,
        });
        if (res.code === "10000") {
          this.baseData = { ...this.baseData, ...res.data };
        }
      } catch (error) {
        console.error("获取仓库详情失败:", error);
      }
    },

    // 库存明细相关方法
    inventoryTabRadioChange(val) {
      this.inventoryCurrentTab = val;
      this.loadInventoryData();
    },

    async loadInventoryData() {
      this.inventoryLoading = true;
      try {
        const params = {
          ...this.inventoryParams,
          repositoryNo: this.baseData.repositoryNo,
          usableStatus: this.inventoryCurrentTab,
          pageNum: this.inventoryTablePage.currentPage,
          pageSize: this.inventoryTablePage.pageSize,
        };

        // 这里需要调用实际的库存明细接口
        const res = await api.getDetailList(params);
        this.inventoryTableData = res.data || [];
        this.inventoryTablePage.total = res.total || 0;
      } catch (error) {
        console.error("加载库存明细失败:", error);
      } finally {
        this.inventoryLoading = false;
      }
    },

    handleInventoryQuery() {
      this.inventoryTablePage.currentPage = 1;
      this.loadInventoryData();
    },

    handleInventoryReset() {
      this.inventoryParams = initParams(this.inventoryFilterOptions.config);
      this.handleInventoryQuery();
    },

    handleInventoryExport() {
      // 导出库存明细
      console.log("导出库存明细");
    },

    // 入库记录相关方法
    async loadInboundData() {
      this.inboundLoading = true;
      try {
        let params = {
          ...this.inboundParams,
          repositoryNo: this.baseData.repositoryNo,
          pageNum: this.inboundTablePage.currentPage,
          pageSize: this.inboundTablePage.pageSize,
        };
        this.handleTimeRange(params);

        // 这里需要调用实际的入库记录接口
        const res = await inboundApi.getTableData(params);
        this.inboundTableData = res.data || [];
        this.inboundTablePage.total = res.total || 0;
      } catch (error) {
        console.error("加载入库记录失败:", error);
      } finally {
        this.inboundLoading = false;
      }
    },

    handleInboundQuery() {
      this.inboundTablePage.currentPage = 1;
      this.loadInboundData();
    },

    handleInboundReset() {
      this.inboundParams = initParams(this.inboundFilterOptions.config);
      this.handleInboundQuery();
    },

    handleInboundExport() {
      let params = {
        ...this.inboundParams,
        repositoryNo: this.baseData.repositoryNo,
        pageNum: this.inboundTablePage.currentPage,
        pageSize: this.inboundTablePage.pageSize,
      };
      this.handleTimeRange(params);
      this.handleCommonExport(inboundApi.export, params);
    },

    // 出库记录相关方法
    async loadOutboundData() {
      this.outboundLoading = true;
      try {
        const params = {
          ...this.outboundParams,
          repositoryNo: this.baseData.repositoryNo,
          pageNum: this.outboundTablePage.currentPage,
          pageSize: this.outboundTablePage.pageSize,
        };

        // 这里需要调用实际的出库记录接口
        const res = await outboundApi.getTableData(params);
        this.outboundTableData = res.data || [];
        this.outboundTablePage.total = res.total || 0;
      } catch (error) {
        console.error("加载出库记录失败:", error);
      } finally {
        this.outboundLoading = false;
      }
    },

    handleOutboundQuery() {
      this.outboundTablePage.currentPage = 1;
      this.loadOutboundData();
    },

    handleOutboundReset() {
      this.outboundParams = initParams(this.outboundFilterOptions.config);
      this.handleOutboundQuery();
    },

    handleOutboundExport() {
      // 导出出库记录
      console.log("导出出库记录");
    },

    // 调拨记录相关方法
    async loadAllocateData() {
      this.allocateLoading = true;
      try {
        const params = {
          ...this.allocateParams,
          repositoryNo: this.baseData.repositoryNo,
          pageNum: this.allocateTablePage.currentPage,
          pageSize: this.allocateTablePage.pageSize,
        };

        // 这里需要调用实际的调拨记录接口
        const res = await allocateApi.getTableData(params);
        this.allocateTableData = res.data || [];
        this.allocateTablePage.total = res.total || 0;
      } catch (error) {
        console.error("加载调拨记录失败:", error);
      } finally {
        this.allocateLoading = false;
      }
    },

    handleAllocateQuery() {
      this.allocateTablePage.currentPage = 1;
      this.loadAllocateData();
    },

    handleAllocateReset() {
      this.allocateParams = initParams(this.allocateFilterOptions.config);
      this.handleAllocateQuery();
    },

    handleAllocateExport() {
      // 导出调拨记录
      console.log("导出调拨记录");
    },

    // 行合并方法
    mergeRowMethod({ row, column, rowIndex, columnIndex }) {
      // 这里实现行合并逻辑，根据具体需求来实现
      // 例如：入库单号相同的行需要合并
      return [1, 1];
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "operTimeRange",
          title: "操作时间",
          startFieldName: "operTimeStart",
          endFieldName: "operTimeEnd",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.operatorOptions = data;
    },
    // 加载仓库选项
    async loadWarehouseOptions() {
      try {
        const res = await inboundApi.queryWarehouseList();
        this.warehouseOptions =
          res.data?.map((item) => ({
            label: item.repositoryName,
            value: item.repositoryName,
          })) || [];
      } catch (error) {
        console.error("加载仓库选项失败:", error);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-btn {
  display: flex;
  justify-content: center;
  .el-button + .el-button {
    margin-left: 40px;
  }
  .el-button--medium {
    font-size: 16px;
    border-radius: 4px;
    padding: 14px 26px;
  }
}

.card-title-wrap {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 16px;

  .card-title-line {
    width: 4px;
    height: 16px;
    background: #409eff;
    margin-right: 8px;
  }
}

.btn-wrap {
  text-align: right;
  padding: 0 20px 20px;
}

/deep/ .el-tabs__content {
  padding: 20px 0;
}

/deep/ .el-card__body {
  padding: 20px;
}
</style>
